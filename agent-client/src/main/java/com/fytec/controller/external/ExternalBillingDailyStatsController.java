package com.fytec.controller.external;

import com.fytec.service.external.impl.ExternalBillingDailyStatsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 能力服务每日使用统计表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/external/billing-stats")
@Tag(name = "外部计费统计", description = "能力服务每日使用统计相关接口")
@RequiredArgsConstructor
public class ExternalBillingDailyStatsController {

    private final ExternalBillingDailyStatsService externalBillingDailyStatsService;

}
