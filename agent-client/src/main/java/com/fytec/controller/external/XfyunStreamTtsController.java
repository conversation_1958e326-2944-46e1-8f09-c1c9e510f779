package com.fytec.controller.external;

import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.dto.external.TtsRequestDTO;
import com.fytec.service.external.StreamTtsService;
import com.fytec.util.R;
import com.fytec.utils.external.XfyunAudioUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;

/**
 * 流式语音合成控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/tts-stream/")
@Tag(name = "讯飞语音合成", description = "讯飞语音合成相关接口")
@RequiredArgsConstructor
public class XfyunStreamTtsController {
    
    private final StreamTtsService streamTtsService;
    
    @PostMapping("/synthesize")
    @Operation(summary = "文本转语音（流式）", description = "将文本转换为语音，返回SSE流")
    public SseEmitter synthesizeText(@Valid @RequestBody TtsRequestDTO request) {
        log.info("收到文本转语音请求，文本长度: {}, 发音人: {}, 音频格式: {}, 超时时间: {}ms",
                request.getText().length(), request.getVcn(), request.getAue(), request.getTimeout());

        return streamTtsService.synthesizeText(request);
    }

    @PostMapping("/synthesize-sync")
    @Operation(summary = "文本转语音（同步）", description = "将文本转换为语音，同步返回结果")
    public R<Object> synthesizeTextSync(@Valid @RequestBody TtsRequestDTO request) {

        log.info("收到同步文本转语音请求，文本长度: {}, 发音人: {}, 音频格式: {}, 超时时间: {}ms",
                request.getText().length(), request.getVcn(), request.getAue(), request.getTimeout());
        return R.ok(streamTtsService.synthesizeTextSync(request));
    }

    @PostMapping("/synthesize-file")
    @Operation(summary = "文本转语音（直接返回文件）", description = "将文本转换为语音，直接返回音频文件流")
    public ResponseEntity<byte[]> synthesizeTextToFile(@Valid @RequestBody TtsRequestDTO request) {

        log.info("收到文件合成请求，文本长度: {}, 发音人: {}, 音频格式: {}, 超时时间: {}ms",
                request.getText().length(), request.getVcn(), request.getAue(), request.getTimeout());

        try {
            // 调用服务获取音频数据
            byte[] audioData = streamTtsService.synthesizeTextToFileBytes(request);

            // 确定文件名和Content-Type
            String audioFormat = XfyunAudioUtil.getAudioFormat(request.getAue());
            String fileName = "tts_audio_" + System.currentTimeMillis() + "." + audioFormat;
            String contentType = XfyunAudioUtil.getContentType(fileName);

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                .body(audioData);

        } catch (Exception e) {
            log.error("文件合成失败，错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/providers")
    @Operation(summary = "获取服务提供商信息", description = "获取当前TTS服务提供商名称")
    public String getProviderInfo() {
        return streamTtsService.getProviderName();
    }
    
    @GetMapping("/status")
    @Operation(summary = "检查服务状态", description = "检查TTS服务是否可用")
    public boolean checkServiceStatus() {
        return streamTtsService.isAvailable();
    }
}
