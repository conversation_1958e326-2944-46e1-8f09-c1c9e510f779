package com.fytec.controller.external;

import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.constant.ExternalConstants;
import com.fytec.service.external.StreamIatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;
import com.fytec.util.R;

/**
 * 流式语音识别控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/iat-stream/")
@Tag(name = "讯飞语音听写(iat-ws)", description = "讯飞语音听写(iat-ws)音频属性:采样率16k、位长16bit、单声道")
@RequiredArgsConstructor
public class XfyunStreamIatController {
    
    private final StreamIatService streamIatService;
    
    @PostMapping("/file")
    @Operation(summary = "音频文件识别", description = "上传音频文件进行流式识别，返回SSE流")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.ASR_REALTIME,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$[0]",
                    processType = ExternalServiceLog.FieldProcessType.MULTIPART_FILE_AUDIO_DURATION
            ),
            needPreHandle = true,
            dataMode = ExternalServiceLog.DataMode.REQUEST
    )
    public SseEmitter recognizeAudioFile(
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "超时时间（毫秒）") @RequestParam(value = "timeout", defaultValue = "600000") Long timeout) {
        return streamIatService.recognizeAudioFile(file, timeout);
    }
    
    @PostMapping("/recognize-sync")
    @Operation(summary = "音频文件识别（同步）", description = "上传音频文件进行同步识别，一次性返回完整结果列表")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.ASR_REALTIME,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$[0]",
                    processType = ExternalServiceLog.FieldProcessType.MULTIPART_FILE_AUDIO_DURATION
            ),
            needPreHandle = true,
            dataMode = ExternalServiceLog.DataMode.REQUEST
    )
    public R<Object> recognizeAudioFileSync(
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "超时时间（毫秒）") @RequestParam(value = "timeout", defaultValue = "30000") Long timeout) {
        try {
            Object result = streamIatService.recognizeAudioFileSync(file, timeout);
            return R.ok(result);
        } catch (Exception e) {
            log.error("同步语音识别失败: {}", e.getMessage(), e);
            return R.failed("语音识别失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/status")
    @Operation(summary = "检查服务状态", description = "检查语音识别服务的可用性")
    public Map<String, Object> checkServiceStatus() {
        return Map.of(
            "provider", streamIatService.getProviderName(),
            "available", streamIatService.isAvailable(),
            "timestamp", System.currentTimeMillis()
        );
    }
}