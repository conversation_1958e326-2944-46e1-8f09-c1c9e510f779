package com.fytec.service.external.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fytec.entity.external.ExternalBillingDailyStats;
import com.fytec.mapper.external.ExternalBillingDailyStatsMapper;
import com.fytec.service.external.ExternalBillingDailyStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 能力服务每日使用统计表 Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ExternalBillingDailyStatsServiceImpl
        extends ServiceImpl<ExternalBillingDailyStatsMapper, ExternalBillingDailyStats>
        implements ExternalBillingDailyStatsService {

}
