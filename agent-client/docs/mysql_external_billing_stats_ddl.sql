CREATE TABLE `external_billing_daily_stats`
(
    `id`                     bigint(20)   NOT NULL COMMENT '主键ID',
    `service_code`           varchar(64)  NOT NULL COMMENT '能力服务编码（对应logType）',
    `service_name`           varchar(255) NOT NULL COMMENT '能力服务名称',
    `stats_date`             date         NOT NULL COMMENT '统计日期',
    `service_count`          bigint(20) DEFAULT 0 COMMENT '服务次数（当日调用总次数）',
    `usage_duration_minutes` bigint(20) DEFAULT 0 COMMENT '使用时长（分钟）-仅语音服务',
    `service_word_count`     bigint(20) DEFAULT 0 COMMENT '服务字数-仅文字识别服务',
    `calculate_count_total`  bigint(20) DEFAULT 0 COMMENT '折算次数总计',
    `deleted`                tinyint(1) DEFAULT NULL COMMENT '是否删除',
    `create_by`              bigint(20) DEFAULT NULL COMMENT '创建者',
    `update_by`              bigint(20) DEFAULT NULL COMMENT '修改者',
    `create_time`            datetime   DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime   DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_stats_date` (`stats_date`),
    KEY `idx_service_code` (`service_code`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='能力服务每日使用统计表';
